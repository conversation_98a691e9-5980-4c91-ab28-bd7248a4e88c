# Base interface for fuzz testing
# https://en.wikipedia.org/wiki/Fuzzing
class_name <PERSON><PERSON><PERSON>
extends RefCounted
# To run a test with a specific fuzzer you have to add defailt argument checked your test case
# all arguments are optional []
# syntax:
# 	func test_foo([fuzzer = <Fuzzer>], [fuzzer_iterations=<amount>], [fuzzer_seed=<number>])
# example:
#   # runs the test 'test_foo' 10 times with a random int value generated by the IntFuzzer
# 	func test_foo(fuzzer = Fuzzers.randomInt(), fuzzer_iterations=10)
#
#   # runs the test 'test_foo2' 1000 times as default with a random seed='*********'
# 	func test_foo2(fuzzer = Fuzzers.randomInt(), fuzzer_seed=*********)

const ITERATION_DEFAULT_COUNT = 1000
const ARGUMENT_FUZZER_INSTANCE := "fuzzer"
const ARGUMENT_ITERATIONS := "fuzzer_iterations"
const ARGUMENT_SEED := "fuzzer_seed"

var _iteration_index :int = 0
var _iteration_limit :int = ITERATION_DEFAULT_COUNT


# generates the next fuzz value
# needs to be implement
func next_value() -> Variant:
	push_error("Invalid vall. Fuzzer not implemented 'next_value()'")
	return null


# returns the current iteration index
func iteration_index() -> int:
	return _iteration_index


# returns the amount of iterations where the fuzzer will be run
func iteration_limit() -> int:
	return _iteration_limit
