using Godot;
using System.Collections.Generic;
using System.Linq;

public partial class CardHolder : Control
{
	[Export] public float MinCardSpacing { get; set; } = 10.0f;
	[Export] public float MaxCardSpacing { get; set; } = 150.0f;
	[Export] public float AnimationDuration { get; set; } = 0.3f;

	private List<Card> cards = new List<Card>();

	public override void _Ready()
	{
		AddToGroup("card_holders");
	}

	public void AddCard(Card card)
	{
		GD.Print($"CardHolder.AddCard called with card: {card?.Title ?? "null"}");
		
		if (card == null)
		{
			GD.PrintErr("Attempted to add null card to CardHolder");
			return;
		}
		
		if (!cards.Contains(card))
		{
			GD.Print($"Adding card {card.Title} to CardHolder. Current card count: {cards.Count}");
			
			cards.Add(card);
			if (card.GetParent() != this)
			{
				card.GetParent()?.RemoveChild(card);
				AddChild(card);
			}
			
			// Ensure proper initialization
			card.SetCardHolder(this);
			
			// Initialize card position and properties
			card.Scale = Vector2.One;
			card.Rotation = 0.0f;
			card.ZIndex = cards.Count - 1;
			
			// Ensure card has proper size before arranging
			if (card.Size == Vector2.Zero)
			{
				card.Size = card.CustomMinimumSize;
			}
			
			// Arrange cards immediately instead of deferred
			ArrangeCards();
			
			GD.Print($"Card {card.Title} added successfully. New card count: {cards.Count}");
		}
		else
		{
			GD.Print($"Card {card.Title} already exists in CardHolder");
		}
	}

	public void RemoveCard(Card card)
	{
		if (cards.Contains(card))
		{
			cards.Remove(card);
			ArrangeCards();
		}
	}

	public void ArrangeCards()
	{
		if (cards.Count == 0) return;
		ArrangeCardsHorizontally();
	}



	private void ArrangeCardsHorizontally()
	{
		if (cards.Count == 0) return;
		
		// Ensure all cards have proper size
		foreach (var card in cards)
		{
			if (card.Size == Vector2.Zero)
			{
				card.Size = card.CustomMinimumSize;
			}
		}
		
		// Get card dimensions
		float cardWidth = cards[0].Size.X;
		float cardHeight = cards[0].Size.Y;
		
		// Calculate available width for cards (with padding)
		float padding = 20.0f;
		float availableWidth = Size.X - (2 * padding);
		
		// Calculate optimal spacing
		float spacing = CalculateOptimalSpacing(cardWidth, availableWidth);
		
		// Calculate total width of all cards including spacing
		float totalCardsWidth = cards.Count * cardWidth;
		float totalSpacingWidth = (cards.Count - 1) * spacing;
		float totalWidth = totalCardsWidth + totalSpacingWidth;
		
		// Calculate starting X position to center all cards
		float startX = (Size.X - totalWidth) / 2;
		
		// Position each card with minimal vertical movement
		for (int i = 0; i < cards.Count; i++)
		{
			Card card = cards[i];
			
			Vector2 targetPosition = new Vector2(
				startX + (i * (cardWidth + spacing)),
				(Size.Y - cardHeight) / 2  // Center vertically
			);
			
			// Set initial position to avoid vertical movement during animation
			if (card.Position.Y == 0 || Mathf.Abs(card.Position.Y - targetPosition.Y) > 5)
			{
				card.Position = new Vector2(card.Position.X, targetPosition.Y);
			}
			
			// Create animation with minimal movement
			var tween = CreateTween();
			tween.SetParallel(true);
			
			// Add minimal delay for staggered effect
			float delay = i * 0.02f;
			
			// Only animate horizontal position to avoid vertical movement
			tween.TweenProperty(card, "position:x", targetPosition.X, AnimationDuration)
				 .SetDelay(delay)
				 .SetEase(Tween.EaseType.Out)
				 .SetTrans(Tween.TransitionType.Quart);
			
			// Ensure vertical position is set immediately without animation
			card.Position = new Vector2(card.Position.X, targetPosition.Y);
			
			// Rotation animation (ensure cards are straight)
			tween.TweenProperty(card, "rotation", 0.0f, AnimationDuration * 0.8f)
				 .SetDelay(delay)
				 .SetEase(Tween.EaseType.Out)
				 .SetTrans(Tween.TransitionType.Quart);
			
			// Minimal scale animation to reduce visual movement
			card.Scale = Vector2.One * 0.99f;
			tween.TweenProperty(card, "scale", Vector2.One, AnimationDuration * 0.3f)
				 .SetDelay(delay)
				 .SetEase(Tween.EaseType.Out)
				 .SetTrans(Tween.TransitionType.Quart);
			
			// Set Z-index for proper layering
			card.ZIndex = i;
		}
	}
	
	private float CalculateOptimalSpacing(float cardWidth, float availableWidth)
	{
		if (cards.Count <= 1) return 0;
		
		// Calculate spacing needed to distribute cards evenly
		float totalCardWidth = cards.Count * cardWidth;
		float remainingSpace = availableWidth - totalCardWidth;
		float idealSpacing = remainingSpace / (cards.Count - 1);
		
		// Clamp spacing to reasonable bounds
		return Mathf.Clamp(idealSpacing, MinCardSpacing, MaxCardSpacing);
	}

	public override void _Draw()
	{
		// Draw a subtle background for the card holder
		DrawRect(new Rect2(Vector2.Zero, Size), new Color(0.1f, 0.1f, 0.2f, 0.3f));
		DrawRect(new Rect2(Vector2.Zero, Size), new Color(0.5f, 0.5f, 0.6f, 0.5f), false, 2.0f);
	}
}
