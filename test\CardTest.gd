extends GdUnitTestSuite

# GdUnit4 test suite for Card functionality
# This tests the Card class methods and properties

func before():
	# Setup before each test
	pass

func after():
	# Cleanup after each test
	pass

func test_card_creation():
	# Test creating a card instance
	var card_scene = load("res://Card.tscn")
	var card = card_scene.instantiate()
	
	assert_that(card).is_not_null()
	assert_that(card.get_script()).is_not_null()
	
	card.queue_free()

func test_card_properties():
	# Test card property setters and getters
	var card_scene = load("res://Card.tscn")
	var card = card_scene.instantiate()
	
	# Set properties
	card.Title = "Fire Dragon"
	card.Attack = 8
	card.Health = 6
	card.Speed = 4
	card.DeploymentTime = 3
	
	# Verify properties
	assert_that(card.Title).is_equal("Fire Dragon")
	assert_that(card.Attack).is_equal(8)
	assert_that(card.Health).is_equal(6)
	assert_that(card.Speed).is_equal(4)
	assert_that(card.DeploymentTime).is_equal(3)
	
	card.queue_free()

func test_card_image_property():
	# Test card image property
	var card_scene = load("res://Card.tscn")
	var card = card_scene.instantiate()
	
	var test_texture = load("res://icon.svg")
	card.CardImage = test_texture
	
	assert_that(card.CardImage).is_not_null()
	assert_that(card.CardImage).is_equal(test_texture)
	
	card.queue_free()

func test_card_display_update():
	# Test card display update functionality
	var card_scene = load("res://Card.tscn")
	var card = card_scene.instantiate()
	
	# Add to scene tree so nodes can be found
	add_child(card)
	await get_tree().process_frame
	
	# Update properties
	card.Title = "Updated Card"
	card.Attack = 10
	card.Health = 8
	card.Speed = 6
	card.DeploymentTime = 4
	
	# Call update method
	card.UpdateCardDisplay()
	
	# Verify properties are still correct
	assert_that(card.Title).is_equal("Updated Card")
	assert_that(card.Attack).is_equal(10)
	assert_that(card.Health).is_equal(8)
	assert_that(card.Speed).is_equal(6)
	assert_that(card.DeploymentTime).is_equal(4)
	
	card.queue_free()

func test_card_holder_assignment():
	# Test setting card holder
	var card_scene = load("res://Card.tscn")
	var card = card_scene.instantiate()
	
	var card_holder_scene = load("res://CardHolder.tscn")
	var card_holder = card_holder_scene.instantiate()
	
	# This should not throw an error
	card.SetCardHolder(card_holder)
	
	assert_that(card).is_not_null()
	
	card.queue_free()
	card_holder.queue_free()