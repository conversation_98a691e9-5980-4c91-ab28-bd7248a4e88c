<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>GdUnit4 Report</title>
	<link rel="stylesheet" href="css/styles.css">
</head>

<body>
	<header>
		<div class="logo">
			<img src="css/logo.png" alt="GdUnit4 Logo">
			<span>GdUnit4</span>
		</div>
		<div class="report-container">
			<h1>Summary Report</h1>
			<div class="summary">
				<div class="summary-item">
					<span class="label">Test Suites</span>
					<span class="value">${suite_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Tests</span>
					<span class="value">${test_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Skipped</span>
					<span class="value">${skipped_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Flaky</span>
					<span class="value">${flaky_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Failures</span>
					<span class="value">${failure_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Orphans</span>
					<span class="value">${orphan_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Duration</span>
					<span class="value">${duration}</span>
				</div>
				<div class="success-rate">
					<div class="check-icon status-${report_state}">✓</div>
					<div class="rate-text">
						<span class="label">Success Rate</span>
						<span class="value">${success_percent}</span>
					</div>
				</div>
			</div>
		</div>
	</header>
	<main>
		<nav>
			<ul>
				<li class="active" data-page="test-suites">TEST SUITES</li>
				<li data-page="paths">PATHS</li>
				<li data-page="logging">LOGGING</li>
			</ul>
		</nav>
		<div id="content">
			<!-- Content will be loaded here based on selected page -->
		</div>
	</main>

	<footer>
		<p>Generated by <a href="https://github.com/MikeSchulze/gdUnit4">GdUnit4</a> at ${buid_date}</p>
		<div class="status-legend">
			<span class="status-legend-item">
				<span class="status-box status-skipped"></span> Skipped
			</span>
			<span class="status-legend-item">
				<span class="status-box status-passed"></span> Passed
			</span>
			<span class="status-legend-item">
				<span class="status-box status-flaky"></span> Flaky
			</span>
			<span class="status-legend-item">
				<span class="status-box status-warning"></span> Warning
			</span>
			<span class="status-legend-item">
				<span class="status-box status-failed"></span> Failed
			</span>
			<span class="status-legend-item">
				<span class="status-box status-error"></span> Error
			</span>
		</div>
	</footer>

	<script>
		// Simple JavaScript to handle page switching
		document.querySelectorAll('nav li').forEach(item => {
			item.addEventListener('click', function () {
				document.querySelectorAll('nav li').forEach(li => li.classList.remove('active'));
				this.classList.add('active');
				loadPage(this.getAttribute('data-page'));
			});
		});

		function loadPage(page) {
			if (page === 'test-suites') {
				document.getElementById('content').innerHTML = `
					<div class="grid-item tab">
						<table>
							<thead>
								<tr>
									<th>Test Suites</th>
									<th>State</th>
									<th>Tests</th>
									<th>Skipped</th>
									<th>Flaky</th>
									<th>Failures</th>
									<th>Orphans</th>
									<th>Duration</th>
									<th>Quick State</th>
								</tr>
							</thead>
							<tbody>
								${report_table_testsuites}
							</tbody>
						</table>
					</div>`
			} else if (page === 'paths') {
				document.getElementById('content').innerHTML = `
					<div class="grid-item tab">
						<table>
							<thead>
								<tr>
									<th>Paths</th>
									<th>State</th>
									<th>Tests</th>
									<th>Skipped</th>
									<th>Flaky</th>
									<th>Failures</th>
									<th>Orphans</th>
									<th>Duration</th>
									<th>Quick State</th>
								</tr>
							</thead>
							<tbody>
								${report_table_paths}
							</tbody>
						</table>
					</div>`
			} else if (page === 'logging') {
				document.getElementById('content').innerHTML = `
					<h3>${godot_log_file}</h3>
					<div class="logging-container">
						<iframe id="logging_content" src="${log_report}"  frameborder="0" height="100%" width="100%"></iframe>
					</div>`
			}
		}

		// Load default page
		loadPage('test-suites');
	</script>
</body>

</html>
