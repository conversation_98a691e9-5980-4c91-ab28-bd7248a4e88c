[gd_scene load_steps=4 format=3 uid="uid://bvn8k2x3napqr"]

[ext_resource type="Script" uid="uid://cykjmwtlcu6wq" path="res://Player.cs" id="1_player"]
[ext_resource type="PackedScene" uid="uid://cw6r5nsugqmyh" path="res://Battlefield.tscn" id="2_battlefield"]
[ext_resource type="PackedScene" uid="uid://hn2hnlbujrgo" path="res://Hand.tscn" id="3_hand"]

[node name="Player" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_player")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="NameLabel" type="Label" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 0
text = "Player"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Battlefield" parent="VBoxContainer" instance=ExtResource("2_battlefield")]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
size_flags_stretch_ratio = 2.0

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2
size_flags_horizontal = 3

[node name="Hand" parent="VBoxContainer" instance=ExtResource("3_hand")]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
