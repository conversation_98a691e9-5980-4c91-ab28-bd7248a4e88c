[gd_scene load_steps=3 format=3 uid="uid://c3nedes1xppc6"]

[ext_resource type="Script" uid="uid://5gf61duc7r0g" path="res://Card.cs" id="1_1a2b3"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.2, 0.2, 0.3, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.8, 0.8, 0.9, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="Card" type="Control"]
custom_minimum_size = Vector2(120, 180)
layout_mode = 3
anchors_preset = 0
script = ExtResource("1_1a2b3")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 5.0
offset_top = 5.0
offset_right = -5.0
offset_bottom = -5.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
custom_minimum_size = Vector2(20, 40)
layout_mode = 2
text = "Card Title"
horizontal_alignment = 1
autowrap_mode = 2

[node name="ImageRect" type="TextureRect" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
expand_mode = 1
stretch_mode = 5

[node name="StatsContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="AttackLabel" type="Label" parent="VBoxContainer/StatsContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "ATK: 0"
horizontal_alignment = 1

[node name="HealthLabel" type="Label" parent="VBoxContainer/StatsContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "HP: 0"
horizontal_alignment = 1

[node name="SpeedLabel" type="Label" parent="VBoxContainer/StatsContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "SPD: 0"
horizontal_alignment = 1

[node name="DeploymentLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Deploy: 0"
horizontal_alignment = 1
