using Godot;

public partial class BattlefieldSlot : Control
{
	[Export] public int SlotIndex { get; set; } = 0;
	
	private Card currentCard = null;
	private bool isHighlighted = false;

	public override void _Ready()
	{
		AddToGroup("battlefield_slots");
		
		// Set up mouse detection
		MouseEntered += OnMouseEntered;
		MouseExited += OnMouseExited;
	}

	public bool HasCard()
	{
		return currentCard != null;
	}

	public Card GetCard()
	{
		return currentCard;
	}

	public void SummonCard(Card card)
	{
		// If there's already a card, discard it
		if (HasCard())
		{
			DiscardCurrentCard();
		}

		// Add the new card
		currentCard = card;
		
		// Remove card from its current parent
		if (card.GetParent() != null)
		{
			card.GetParent().RemoveChild(card);
		}
		
		// Add card as child of this slot
		AddChild(card);
		
		// Position the card in the center of the slot
		card.Position = (Size - card.Size) / 2;
		card.Scale = Vector2.One;
		card.Rotation = 0.0f;
		card.ZIndex = 1;
		
		// Animate the card appearing
		AnimateCardSummon(card);
		
		GD.Print($"Card '{card.Title}' summoned to slot {SlotIndex}");
	}

	private void DiscardCurrentCard()
	{
		if (currentCard != null)
		{
			GD.Print($"Discarding card '{currentCard.Title}' from slot {SlotIndex}");
			
			// Animate card being discarded
			AnimateCardDiscard(currentCard);
			
			currentCard = null;
		}
	}

	public void ClearSlot()
	{
		if (HasCard())
		{
			currentCard.QueueFree();
			currentCard = null;
		}
	}

	private void AnimateCardSummon(Card card)
	{
		// Start with card slightly scaled down and fade in
		card.Scale = Vector2.One * 0.8f;
		card.Modulate = new Color(1, 1, 1, 0);
		
		var tween = CreateTween();
		tween.SetParallel(true);
		
		tween.TweenProperty(card, "scale", Vector2.One, 0.3f)
			 .SetEase(Tween.EaseType.Out)
			 .SetTrans(Tween.TransitionType.Back);
			 
		tween.TweenProperty(card, "modulate", Colors.White, 0.2f)
			 .SetEase(Tween.EaseType.Out);
	}

	private void AnimateCardDiscard(Card card)
	{
		var tween = CreateTween();
		tween.SetParallel(true);
		
		tween.TweenProperty(card, "scale", Vector2.One * 0.1f, 0.2f)
			 .SetEase(Tween.EaseType.In)
			 .SetTrans(Tween.TransitionType.Back);
			 
		tween.TweenProperty(card, "modulate", new Color(1, 1, 1, 0), 0.2f)
			 .SetEase(Tween.EaseType.In);
			 
		tween.TweenCallback(Callable.From(() => card.QueueFree())).SetDelay(0.2f);
	}

	private void OnMouseEntered()
	{
		isHighlighted = true;
		QueueRedraw();
	}

	private void OnMouseExited()
	{
		isHighlighted = false;
		QueueRedraw();
	}

	public override void _Draw()
	{
		// Draw slot background
		var bgColor = isHighlighted ? new Color(0.3f, 0.3f, 0.4f, 0.6f) : new Color(0.2f, 0.2f, 0.3f, 0.4f);
		DrawRect(new Rect2(Vector2.Zero, Size), bgColor);
		
		// Draw slot border
		var borderColor = isHighlighted ? new Color(0.6f, 0.6f, 0.8f, 0.8f) : new Color(0.4f, 0.4f, 0.5f, 0.6f);
		DrawRect(new Rect2(Vector2.Zero, Size), borderColor, false, 2.0f);
		
		// Draw slot number
		var font = ThemeDB.FallbackFont;
		var fontSize = 16;
		var text = (SlotIndex + 1).ToString();
		var textSize = font.GetStringSize(text, HorizontalAlignment.Left, -1, fontSize);
		var textPos = new Vector2(Size.X - textSize.X - 5, textSize.Y + 5);
		
		DrawString(font, textPos, text, HorizontalAlignment.Left, -1, fontSize, new Color(0.7f, 0.7f, 0.8f, 0.8f));
	}
}