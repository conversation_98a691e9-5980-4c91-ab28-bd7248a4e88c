<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="x-ua-compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>GdUnit4 Testsuite</title>
	<link rel="stylesheet" href="../css/styles.css">
	<link href="../css/breadcrumb.css" rel="stylesheet" type="text/css" />

	<script src="https://code.jquery.com/jquery-3.6.4.js"></script>
	<script>
		$(document).ready(function () {
			var report_view = $('#report_area').find('.report-column')

			$('#report-table tr').click(function () {
				var report = $(this).find('.report-column').html();
				report_view.html(report)
				$('.selected').removeClass('selected');
				$(this).addClass('selected');
			});
		});
	</script>
</head>

<body>
	<header>
		<div class="logo">
			<img src="../css/logo.png" alt="GdUnit4 Logo">
			<span>GdUnit4</span>
		</div>
		<div class="report-container">
			<h1>Testsuite Report</h1>
			<div>
				<span class="label">${resource_path}</span>
			</div>
			<div class="summary">
				<div class="summary-item">
					<span class="label">Tests</span>
					<span class="value">${test_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Skipped</span>
					<span class="value">${skipped_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Flaky</span>
					<span class="value">${flaky_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Failures</span>
					<span class="value">${failure_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Orphans</span>
					<span class="value">${orphan_count}</span>
				</div>
				<div class="summary-item">
					<span class="label">Duration</span>
					<span class="value">${duration}</span>
				</div>
				<div class="success-rate">
					<div class="check-icon status-${report_state}">✓</div>
					<div class="rate-text">
						<span class="label">Success Rate</span>
						<span class="value">${success_percent}</span>
					</div>
				</div>
			</div>
		</div>
		<div class="breadcrumb">
			<a href="../index.html"><span>All</span></a>
			<a href="${breadcrumb_path_link}"><span>${path}</span></a>
			<a class="active" href="#"><span>${testsuite_name}</span></a>
		</div>
	</header>

	<main>
		<div class="tab-report-grid">
			<div class="grid-item tab">
				<table id="report-table">
					<thead>
						<tr>
							<th>Testcase</th>
							<th>Result</th>
							<th>Skipped</th>
							<th>Orphans</th>
							<th>Duration</th>
							<th class="report-column">Report</th>
						</tr>
					</thead>
					<tbody>
						${report_table_tests}
					</tbody>
				</table>
			</div>
			<div id="report_area" class="grid-item tab">
				<h4>Failure Report</h4>
				<div class="report-column"></div>
			</div>
		</div>
	</main>

	<footer>
		<p>Generated by <a href="https://github.com/MikeSchulze/gdUnit4">GdUnit4</a> at ${buid_date}</p>
		<div class="status-legend">
			<span class="status-legend-item">
				<span class="status-box status-skipped"></span> Skipped
			</span>
			<span class="status-legend-item">
				<span class="status-box status-passed"></span> Passed
			</span>
			<span class="status-legend-item">
				<span class="status-box status-flaky"></span> Flaky
			</span>
			<span class="status-legend-item">
				<span class="status-box status-warning"></span> Warning
			</span>
			<span class="status-legend-item">
				<span class="status-box status-failed"></span> Failed
			</span>
			<span class="status-legend-item">
				<span class="status-box status-error"></span> Error
			</span>
		</div>
	</footer>
</body>

<script>
	function groupTableRows() {
		const table = document.getElementById('report-table');
		const rows = table.querySelectorAll('tbody tr');

		let previousTestCase = '';
		let groupStartIndex = 0;
		let groupColorToggle = true; // Toggle between two colors

		rows.forEach((row, index) => {
			const testCaseName = row.cells[0].textContent.trim();

			if (testCaseName !== previousTestCase) {
				// Close the previous group
				if (index > 0 && groupStartIndex !== index - 1) {
					// Apply background color to the group
					const groupClass = groupColorToggle ? 'group-bg-1' : 'group-bg-2';
					for (let i = groupStartIndex; i <= index - 1; i++) {
						rows[i].classList.add(groupClass);
					}

					// Toggle background color for the next group
					groupColorToggle = !groupColorToggle;
				}

				// Start a new group
				groupStartIndex = index;
			}

			previousTestCase = testCaseName;
		});

		// Handle the last group
		if (groupStartIndex !== rows.length - 1) {
			// Apply background color to the last group
			const groupClass = groupColorToggle ? 'group-bg-1' : 'group-bg-2';
			for (let i = groupStartIndex; i < rows.length; i++) {
				rows[i].classList.add(groupClass);
			}
		}
	}

	// Call the function to group table rows after the DOM loads
	document.addEventListener('DOMContentLoaded', groupTableRows);
</script>

</html>
