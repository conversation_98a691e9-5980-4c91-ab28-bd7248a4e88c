extends GdUnitTestSuite

# GdUnit4 test suite for Player functionality
# This tests the Player class methods and card management

var player: Player

func before():
	# Setup before each test
	var player_scene = load("res://Player.tscn")
	player = player_scene.instantiate()
	player.PlayerName = "TestPlayer"
	player.StartingCardCount = 3
	add_child(player)

func after():
	# Cleanup after each test
	if player:
		player.queue_free()
		player = null

func test_player_initialization():
	# Test player basic properties
	assert_that(player).is_not_null()
	assert_that(player.PlayerName).is_equal("TestPlayer")
	assert_that(player.StartingCardCount).is_equal(3)
	assert_that(player.IsOpponent).is_false()

func test_player_properties():
	# Test property setters
	player.PlayerName = "Fire Mage"
	player.IsOpponent = true
	player.StartingCardCount = 5
	
	assert_that(player.PlayerName).is_equal("Fire Mage")
	assert_that(player.IsOpponent).is_true()
	assert_that(player.StartingCardCount).is_equal(5)

func test_player_components_after_ready():
	# Wait for player initialization
	await get_tree().process_frame
	await get_tree().process_frame
	
	var hand_card_holder = player.GetHandCardHolder()
	var battlefield = player.GetBattlefield()
	
	assert_that(hand_card_holder).is_not_null()
	assert_that(battlefield).is_not_null()

func test_starting_card_spawning():
	# Wait for player initialization and card spawning
	await get_tree().process_frame
	await get_tree().process_frame
	await get_tree().create_timer(0.3).timeout
	
	var hand_card_count = player.GetHandCardCount()
	assert_that(hand_card_count).is_equal(player.StartingCardCount)

func test_add_card_to_hand():
	# Wait for initialization
	await get_tree().process_frame
	await get_tree().process_frame
	await get_tree().create_timer(0.3).timeout
	
	var initial_count = player.GetHandCardCount()
	var test_card_data = CardData.new("Test Card", 5, 3, 2, 1, "icon.svg")
	
	player.AddCardToHand(test_card_data)
	await get_tree().process_frame
	
	var new_count = player.GetHandCardCount()
	assert_that(new_count).is_equal(initial_count + 1)

func test_draw_card():
	# Wait for initialization
	await get_tree().process_frame
	await get_tree().process_frame
	await get_tree().create_timer(0.3).timeout
	
	var initial_count = player.GetHandCardCount()
	
	player.DrawCard()
	await get_tree().process_frame
	
	var new_count = player.GetHandCardCount()
	assert_that(new_count).is_greater_than(initial_count)

func test_get_hand_cards():
	# Wait for initialization
	await get_tree().process_frame
	await get_tree().process_frame
	await get_tree().create_timer(0.3).timeout
	
	var hand_cards = player.GetHandCards()
	assert_that(hand_cards).is_not_null()
	assert_that(hand_cards.size()).is_equal(player.StartingCardCount)
	
	# Verify cards have valid properties
	for card in hand_cards:
		assert_that(card).is_not_null()
		assert_that(card.Title).is_not_empty()
		assert_that(card.Attack).is_greater_equal(0)
		assert_that(card.Health).is_greater_than(0)

func test_battlefield_card_management():
	# Wait for initialization
	await get_tree().process_frame
	await get_tree().process_frame
	await get_tree().create_timer(0.3).timeout
	
	var initial_battlefield_count = player.GetBattlefieldCardCount()
	var test_card_data = CardData.new("Battlefield Card", 4, 6, 3, 2, "icon.svg")
	
	player.AddCardToBattlefield(test_card_data)
	await get_tree().process_frame
	
	var new_battlefield_count = player.GetBattlefieldCardCount()
	assert_that(new_battlefield_count).is_equal(initial_battlefield_count + 1)

func test_empty_and_occupied_slots():
	# Wait for initialization
	await get_tree().process_frame
	await get_tree().process_frame
	await get_tree().create_timer(0.3).timeout
	
	var empty_slots = player.GetEmptySlots()
	var occupied_slots = player.GetOccupiedSlots()
	
	assert_that(empty_slots).is_not_null()
	assert_that(occupied_slots).is_not_null()
	
	# Initially, all slots should be empty
	assert_that(occupied_slots.size()).is_equal(0)
	assert_that(empty_slots.size()).is_greater_than(0)
	
	# Add a card to battlefield
	var test_card_data = CardData.new("Slot Test Card", 3, 4, 2, 1, "icon.svg")
	player.AddCardToBattlefield(test_card_data)
	await get_tree().process_frame
	
	var new_empty_slots = player.GetEmptySlots()
	var new_occupied_slots = player.GetOccupiedSlots()
	
	assert_that(new_occupied_slots.size()).is_equal(1)
	assert_that(new_empty_slots.size()).is_equal(empty_slots.size() - 1)

func test_opponent_flag():
	# Test opponent flag functionality
	player.IsOpponent = true
	assert_that(player.IsOpponent).is_true()
	
	player.IsOpponent = false
	assert_that(player.IsOpponent).is_false()