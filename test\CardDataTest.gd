extends GdUnitTestSuite

# GdUnit4 test suite for CardData functionality
# This tests the CardData class properties and methods

func test_card_data_creation():
	# Test creating a CardData instance
	var card_data = CardData.new("Fire Dragon", 8, 6, 4, 3, "fire_dragon.svg")
	
	assert_that(card_data).is_not_null()
	assert_that(card_data.Title).is_equal("Fire Dragon")
	assert_that(card_data.Attack).is_equal(8)
	assert_that(card_data.Health).is_equal(6)
	assert_that(card_data.Speed).is_equal(4)
	assert_that(card_data.DeploymentTime).is_equal(3)
	assert_that(card_data.ImagePath).is_equal("fire_dragon.svg")

func test_card_data_properties():
	# Test property setters
	var card_data = CardData.new("Test Card", 1, 1, 1, 1, "test.svg")
	
	# Test property setters
	card_data.Title = "Ice Wizard"
	card_data.Attack = 4
	card_data.Health = 3
	card_data.Speed = 5
	card_data.DeploymentTime = 2
	card_data.ImagePath = "ice_wizard.svg"
	
	assert_that(card_data.Title).is_equal("Ice Wizard")
	assert_that(card_data.Attack).is_equal(4)
	assert_that(card_data.Health).is_equal(3)
	assert_that(card_data.Speed).is_equal(5)
	assert_that(card_data.DeploymentTime).is_equal(2)
	assert_that(card_data.ImagePath).is_equal("ice_wizard.svg")

func test_card_data_with_zero_values():
	# Test CardData with zero values
	var card_data = CardData.new("Zero Card", 0, 1, 0, 0, "")
	
	assert_that(card_data.Title).is_equal("Zero Card")
	assert_that(card_data.Attack).is_equal(0)
	assert_that(card_data.Health).is_equal(1)
	assert_that(card_data.Speed).is_equal(0)
	assert_that(card_data.DeploymentTime).is_equal(0)
	assert_that(card_data.ImagePath).is_equal("")

func test_card_data_with_high_values():
	# Test CardData with high values
	var card_data = CardData.new("Legendary Dragon", 15, 20, 10, 8, "legendary.svg")
	
	assert_that(card_data.Title).is_equal("Legendary Dragon")
	assert_that(card_data.Attack).is_equal(15)
	assert_that(card_data.Health).is_equal(20)
	assert_that(card_data.Speed).is_equal(10)
	assert_that(card_data.DeploymentTime).is_equal(8)
	assert_that(card_data.ImagePath).is_equal("legendary.svg")

func test_card_data_with_empty_title():
	# Test CardData with empty title
	var card_data = CardData.new("", 5, 3, 2, 1, "icon.svg")
	
	assert_that(card_data.Title).is_equal("")
	assert_that(card_data.Attack).is_equal(5)
	assert_that(card_data.Health).is_equal(3)
	assert_that(card_data.Speed).is_equal(2)
	assert_that(card_data.DeploymentTime).is_equal(1)
	assert_that(card_data.ImagePath).is_equal("icon.svg")

func test_card_data_with_long_title():
	# Test CardData with long title
	var long_title = "Ancient Fire-Breathing Dragon of the Molten Mountains"
	var card_data = CardData.new(long_title, 12, 8, 6, 5, "ancient_dragon.svg")
	
	assert_that(card_data.Title).is_equal(long_title)
	assert_that(card_data.Attack).is_equal(12)
	assert_that(card_data.Health).is_equal(8)
	assert_that(card_data.Speed).is_equal(6)
	assert_that(card_data.DeploymentTime).is_equal(5)
	assert_that(card_data.ImagePath).is_equal("ancient_dragon.svg")

func test_multiple_card_data_instances():
	# Test multiple CardData instances maintain separate data
	var card1 = CardData.new("Card 1", 1, 1, 1, 1, "card1.svg")
	var card2 = CardData.new("Card 2", 2, 2, 2, 2, "card2.svg")
	var card3 = CardData.new("Card 3", 3, 3, 3, 3, "card3.svg")
	
	# Verify each card maintains its own data
	assert_that(card1.Title).is_equal("Card 1")
	assert_that(card1.Attack).is_equal(1)
	
	assert_that(card2.Title).is_equal("Card 2")
	assert_that(card2.Attack).is_equal(2)
	
	assert_that(card3.Title).is_equal("Card 3")
	assert_that(card3.Attack).is_equal(3)
	
	# Modify one card and verify others are unaffected
	card2.Title = "Modified Card"
	card2.Attack = 10
	
	assert_that(card1.Title).is_equal("Card 1")
	assert_that(card1.Attack).is_equal(1)
	assert_that(card2.Title).is_equal("Modified Card")
	assert_that(card2.Attack).is_equal(10)
	assert_that(card3.Title).is_equal("Card 3")
	assert_that(card3.Attack).is_equal(3)