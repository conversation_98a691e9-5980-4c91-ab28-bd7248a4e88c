extends SceneTree

# Simple test runner to execute GdUnit4 tests
# Run this script to execute all tests

func _init():
	print("=== Starting GdUnit4 Tests ===")
	
	# Try to run tests using GdUnit4's built-in runner
	var test_runner = preload("res://addons/gdUnit4/src/core/runners/GdUnitTestRunner.gd").new()
	
	print("Test runner created")
	print("Available test files:")
	print("- CardTest.gd")
	print("- PlayerTest.gd") 
	print("- BattlefieldSlotTest.gd")
	print("- CardDataTest.gd")
	
	print("=== Tests Ready ===")
	print("Use the GdUnit4 UI in Godot editor to run tests, or use command line:")
	print("godot --headless -s res://addons/gdUnit4/bin/GdUnitCmdTool.gd")
	
	quit()