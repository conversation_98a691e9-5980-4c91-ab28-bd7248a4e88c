using Godot;
using System.Collections.Generic;

public partial class Player : Control
{
	[Export] public string PlayerName { get; set; } = "Player";
	[Export] public bool IsOpponent { get; set; } = false;
	[Export] public int StartingCardCount { get; set; } = 5;
	
	private CardHolder handCardHolder;
	private Battlefield battlefield;
	private Label nameLabel;
	private List<CardData> availableCards = new List<CardData>();

	public override void _Ready()
	{
		GD.Print($"Player _Ready() called for {PlayerName}");
		InitializePlayer();
		InitializeCardData();
		
		// Use a timer to delay card spawning
		var timer = GetTree().CreateTimer(0.1f);
		timer.Timeout += DealStartingCards;
	}

	private void InitializePlayer()
	{
		try
		{
			handCardHolder = GetNode<CardHolder>("VBoxContainer/Hand");
			battlefield = GetNode<Battlefield>("VBoxContainer/Battlefield");
			nameLabel = GetNode<Label>("VBoxContainer/NameLabel");
			
			nameLabel.Text = PlayerName;
			nameLabel.HorizontalAlignment = HorizontalAlignment.Center;
			
			GD.Print($"Player initialized successfully: {PlayerName}");
			GD.Print($"Hand CardHolder: {handCardHolder != null}");
			GD.Print($"Battlefield: {battlefield != null}");
		}
		catch (System.Exception e)
		{
			GD.PrintErr($"Error initializing player: {e.Message}");
		}
	}

	private void InitializeCardData()
	{
		// Define different card sets for each player
		if (IsOpponent)
		{
			// Opponent's deck - more defensive cards
			availableCards.Add(new CardData("Stone Guardian", 2, 8, 1, 3, "stone_golem.svg"));
			availableCards.Add(new CardData("Ice Barrier", 1, 6, 2, 2, "ice_wizard.svg"));
			availableCards.Add(new CardData("Frost Mage", 4, 3, 5, 2, "ice_wizard.svg"));
			availableCards.Add(new CardData("Rock Titan", 6, 12, 1, 5, "stone_golem.svg"));
			availableCards.Add(new CardData("Crystal Shield", 0, 4, 3, 1, "ice_wizard.svg"));
			availableCards.Add(new CardData("Earth Elemental", 5, 7, 2, 4, "stone_golem.svg"));
			availableCards.Add(new CardData("Frozen Sentinel", 3, 9, 1, 4, "ice_wizard.svg"));
		}
		else
		{
			// Player's deck - more aggressive cards
			availableCards.Add(new CardData("Fire Dragon", 8, 6, 4, 3, "fire_dragon.svg"));
			availableCards.Add(new CardData("Flame Warrior", 6, 4, 6, 2, "fire_dragon.svg"));
			availableCards.Add(new CardData("Inferno Beast", 10, 5, 3, 4, "fire_dragon.svg"));
			availableCards.Add(new CardData("Blazing Phoenix", 7, 3, 8, 2, "fire_dragon.svg"));
			availableCards.Add(new CardData("Ember Spirit", 4, 2, 7, 1, "fire_dragon.svg"));
			availableCards.Add(new CardData("Molten Golem", 5, 8, 2, 3, "fire_dragon.svg"));
			availableCards.Add(new CardData("Fire Elemental", 9, 4, 5, 3, "fire_dragon.svg"));
		}
	}

	private void DealStartingCards()
	{
		// Clear any existing cards first
		foreach (Node child in handCardHolder.GetChildren())
		{
			if (child is Card)
			{
				child.QueueFree();
			}
		}
		
		// Wait a frame for cleanup, then spawn cards
		CallDeferred(nameof(SpawnStartingCards));
	}
	
	private void SpawnStartingCards()
	{
		GD.Print($"SpawnStartingCards called for {PlayerName}");
		GD.Print($"Available cards count: {availableCards.Count}");
		GD.Print($"Starting card count: {StartingCardCount}");
		GD.Print($"Hand card holder is null: {handCardHolder == null}");
		
		if (handCardHolder == null)
		{
			GD.PrintErr("HandCardHolder is null! Cannot spawn cards.");
			return;
		}
		
		for (int i = 0; i < StartingCardCount; i++)
		{
			if (i < availableCards.Count)
			{
				var cardData = availableCards[i];
				GD.Print($"Creating card {i}: {cardData.Title}");
				
				var card = CreateCard(cardData);
				
				if (card == null)
				{
					GD.PrintErr($"Failed to create card {i}");
					continue;
				}
				
				// Ensure card is properly initialized
				card.Visible = true;
				card.Scale = Vector2.One;
				card.Rotation = 0.0f;
				
				handCardHolder.AddCard(card);
				GD.Print($"Added starting card {i}: {cardData.Title}");
			}
		}
		
		GD.Print($"Finished spawning cards. Hand now has {handCardHolder.GetChildren().Count} children");
	}

	private Card CreateCard(CardData cardData)
	{
		try
		{
			var cardScene = GD.Load<PackedScene>("res://Card.tscn");
			if (cardScene == null)
			{
				GD.PrintErr("Failed to load Card.tscn");
				return null;
			}
			
			var card = cardScene.Instantiate<Card>();
			if (card == null)
			{
				GD.PrintErr("Failed to instantiate Card from scene");
				return null;
			}
			
			card.Title = cardData.Title;
			card.Attack = cardData.Attack;
			card.Health = cardData.Health;
			card.Speed = cardData.Speed;
			card.DeploymentTime = cardData.DeploymentTime;
			card.CardImage = LoadCardImage(cardData.ImagePath);
			
			card.Scale = Vector2.One;
			card.Rotation = 0.0f;
			card.ZIndex = 0;
			
			card.CallDeferred(nameof(Card.UpdateCardDisplay));
			
			GD.Print($"Successfully created card: {cardData.Title}");
			return card;
		}
		catch (System.Exception e)
		{
			GD.PrintErr($"Error creating card {cardData.Title}: {e.Message}");
			return null;
		}
	}

	private Texture2D LoadCardImage(string imagePath)
	{
		var texture = GD.Load<Texture2D>($"res://{imagePath}");
		
		if (texture == null)
		{
			GD.PrintErr($"Could not load image at path: res://{imagePath}");
			return GD.Load<Texture2D>("res://icon.svg");
		}
		
		return texture;
	}

	public void AddCardToHand(CardData cardData)
	{
		var card = CreateCard(cardData);
		handCardHolder.AddCard(card);
	}

	public void AddCardToBattlefield(CardData cardData)
	{
		var card = CreateCard(cardData);
		// Find the first available slot and summon the card there
		var slots = battlefield.GetTree().GetNodesInGroup("battlefield_slots");
		foreach (BattlefieldSlot slot in slots)
		{
			if (!slot.HasCard())
			{
				slot.SummonCard(card);
				break;
			}
		}
	}

	public void DrawCard()
	{
		if (availableCards.Count > handCardHolder.GetChildren().Count)
		{
			var nextCardIndex = handCardHolder.GetChildren().Count;
			if (nextCardIndex < availableCards.Count)
			{
				AddCardToHand(availableCards[nextCardIndex]);
			}
		}
	}

	public CardHolder GetHandCardHolder()
	{
		return handCardHolder;
	}

	public Battlefield GetBattlefield()
	{
		return battlefield;
	}

	// Method to manually spawn cards for testing
	public void SpawnTestCards()
	{
		GD.Print("SpawnTestCards() called manually");
		
		// Clear existing cards first
		foreach (Node child in handCardHolder.GetChildren())
		{
			if (child is Card)
			{
				child.QueueFree();
			}
		}
		
		// Wait a frame then spawn new cards
		CallDeferred(nameof(ForceSpawnCards));
	}
	
	private void ForceSpawnCards()
	{
		GD.Print("ForceSpawnCards() - creating cards directly");
		
		for (int i = 0; i < 3; i++) // Start with just 3 cards for testing
		{
			if (i < availableCards.Count)
			{
				var cardData = availableCards[i];
				var card = CreateCard(cardData);
				
				// Set card properties explicitly
				card.Position = new Vector2(i * 130, 0);
				card.Visible = true;
				
				handCardHolder.AddChild(card);
				GD.Print($"Added card {i}: {cardData.Title} at position {card.Position}");
			}
		}
	}



	// Testing helper methods
	public int GetHandCardCount()
	{
		if (handCardHolder == null) return 0;
		
		int count = 0;
		foreach (Node child in handCardHolder.GetChildren())
		{
			if (child is Card) count++;
		}
		return count;
	}
	
	public List<Card> GetHandCards()
	{
		var cards = new List<Card>();
		if (handCardHolder == null) return cards;
		
		foreach (Node child in handCardHolder.GetChildren())
		{
			if (child is Card card) cards.Add(card);
		}
		return cards;
	}
	
	public int GetBattlefieldCardCount()
	{
		var slots = GetTree().GetNodesInGroup("battlefield_slots");
		int count = 0;
		foreach (BattlefieldSlot slot in slots)
		{
			if (slot.HasCard()) count++;
		}
		return count;
	}
	
	public List<BattlefieldSlot> GetOccupiedSlots()
	{
		var occupiedSlots = new List<BattlefieldSlot>();
		var slots = GetTree().GetNodesInGroup("battlefield_slots");
		foreach (BattlefieldSlot slot in slots)
		{
			if (slot.HasCard()) occupiedSlots.Add(slot);
		}
		return occupiedSlots;
	}
	
	public List<BattlefieldSlot> GetEmptySlots()
	{
		var emptySlots = new List<BattlefieldSlot>();
		var slots = GetTree().GetNodesInGroup("battlefield_slots");
		foreach (BattlefieldSlot slot in slots)
		{
			if (!slot.HasCard()) emptySlots.Add(slot);
		}
		return emptySlots;
	}

	// Add input handling to test card spawning
	public override void _Input(InputEvent @event)
	{
		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.Space)
			{
				GD.Print("Space key pressed - spawning test cards");
				SpawnTestCards();
			}
			else if (keyEvent.Keycode == Key.R)
			{
				GD.Print("R key pressed - respawning starting cards");
				DealStartingCards();
			}
		}
	}
}

public class CardData
{
	public string Title { get; set; }
	public int Attack { get; set; }
	public int Health { get; set; }
	public int Speed { get; set; }
	public int DeploymentTime { get; set; }
	public string ImagePath { get; set; }

	public CardData(string title, int attack, int health, int speed, int deploymentTime, string imagePath)
	{
		Title = title;
		Attack = attack;
		Health = health;
		Speed = speed;
		DeploymentTime = deploymentTime;
		ImagePath = imagePath;
	}
}
