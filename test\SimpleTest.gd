extends GdUnitTestSuite

# The simplest possible test
# This test just verifies basic GdUnit4 functionality

func test_simple_assertion():
	# Test that 1 + 1 equals 2
	var result = 1 + 1
	assert_that(result).is_equal(2)

func test_string_assertion():
	# Test basic string functionality
	var greeting = "Hello"
	assert_that(greeting).is_equal("Hello")

func test_boolean_assertion():
	# Test boolean values
	var is_true = true
	assert_that(is_true).is_true()
