extends GdUnitTestSuite

# GdUnit4 test suite for BattlefieldSlot functionality
# This tests the BattlefieldSlot class methods and card management

var slot: BattlefieldSlot
var test_card: Card

func before():
	# Setup before each test
	slot = BattlefieldSlot.new()
	slot.SlotIndex = 0
	slot.size = Vector2(120, 160)
	add_child(slot)
	
	# Create a test card
	var card_scene = load("res://Card.tscn")
	test_card = card_scene.instantiate()
	test_card.Title = "Test Card"
	test_card.Attack = 5
	test_card.Health = 3
	test_card.Speed = 2
	test_card.DeploymentTime = 1

func after():
	# Cleanup after each test
	if test_card:
		test_card.queue_free()
		test_card = null
	if slot:
		slot.queue_free()
		slot = null

func test_slot_initialization():
	# Test basic slot properties
	assert_that(slot).is_not_null()
	assert_that(slot.SlotIndex).is_equal(0)
	assert_that(slot.HasCard()).is_false()
	assert_that(slot.GetCard()).is_null()

func test_slot_index_property():
	# Test slot index setter
	slot.SlotIndex = 5
	assert_that(slot.SlotIndex).is_equal(5)

func test_card_summoning():
	# Test summoning a card to the slot
	await get_tree().process_frame
	
	assert_that(slot.HasCard()).is_false()
	
	slot.SummonCard(test_card)
	await get_tree().process_frame
	
	assert_that(slot.HasCard()).is_true()
	assert_that(slot.GetCard()).is_equal(test_card)
	assert_that(test_card.get_parent()).is_equal(slot)

func test_card_replacement():
	# Test replacing a card in the slot
	await get_tree().process_frame
	
	# Summon first card
	slot.SummonCard(test_card)
	await get_tree().process_frame
	
	assert_that(slot.HasCard()).is_true()
	assert_that(slot.GetCard()).is_equal(test_card)
	
	# Create and summon second card
	var card_scene = load("res://Card.tscn")
	var second_card = card_scene.instantiate()
	second_card.Title = "Replacement Card"
	second_card.Attack = 3
	second_card.Health = 4
	
	slot.SummonCard(second_card)
	await get_tree().process_frame
	
	assert_that(slot.HasCard()).is_true()
	assert_that(slot.GetCard()).is_equal(second_card)
	assert_that(slot.GetCard().Title).is_equal("Replacement Card")
	
	second_card.queue_free()

func test_slot_clearing():
	# Test clearing the slot
	await get_tree().process_frame
	
	# Summon a card
	slot.SummonCard(test_card)
	await get_tree().process_frame
	
	assert_that(slot.HasCard()).is_true()
	
	# Clear the slot
	slot.ClearSlot()
	await get_tree().process_frame
	
	assert_that(slot.HasCard()).is_false()
	assert_that(slot.GetCard()).is_null()

func test_card_positioning():
	# Test that card is positioned correctly in slot
	await get_tree().process_frame
	
	slot.SummonCard(test_card)
	await get_tree().process_frame
	
	# Verify card is positioned within the slot
	var card_position = test_card.position
	assert_that(card_position.x).is_greater_equal(0)
	assert_that(card_position.y).is_greater_equal(0)
	
	# Card should be centered in the slot (approximately)
	var expected_x = (slot.size.x - test_card.size.x) / 2
	var expected_y = (slot.size.y - test_card.size.y) / 2
	
	# Allow some tolerance for positioning
	assert_that(card_position.x).is_equal_approx(expected_x, 5.0)
	assert_that(card_position.y).is_equal_approx(expected_y, 5.0)

func test_card_properties_after_summon():
	# Test card properties are set correctly after summoning
	await get_tree().process_frame
	
	slot.SummonCard(test_card)
	await get_tree().process_frame
	
	var summoned_card = slot.GetCard()
	assert_that(summoned_card.scale).is_equal(Vector2.ONE)
	assert_that(summoned_card.rotation).is_equal(0.0)
	assert_that(summoned_card.z_index).is_equal(1)

func test_slot_group_membership():
	# Test that slot is added to the correct group
	await get_tree().process_frame
	
	# Verify slot is added to the correct group
	assert_that(slot.is_in_group("battlefield_slots")).is_true()

func test_multiple_card_operations():
	# Test sequence of operations: empty -> card -> clear -> new card
	await get_tree().process_frame
	
	# Test sequence: empty -> card -> clear -> new card
	assert_that(slot.HasCard()).is_false()
	
	slot.SummonCard(test_card)
	await get_tree().process_frame
	assert_that(slot.HasCard()).is_true()
	
	slot.ClearSlot()
	await get_tree().process_frame
	assert_that(slot.HasCard()).is_false()
	
	# Create new card
	var card_scene = load("res://Card.tscn")
	var new_card = card_scene.instantiate()
	new_card.Title = "New Card"
	
	slot.SummonCard(new_card)
	await get_tree().process_frame
	assert_that(slot.HasCard()).is_true()
	assert_that(slot.GetCard().Title).is_equal("New Card")
	
	new_card.queue_free()