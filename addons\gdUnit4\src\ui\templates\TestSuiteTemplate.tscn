[gd_scene load_steps=2 format=3 uid="uid://dte0m2endcgtu"]

[ext_resource type="Script" path="res://addons/gdUnit4/src/ui/templates/TestSuiteTemplate.gd" id="1"]

[node name="TestSuiteTemplate" type="MarginContainer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
script = ExtResource("1")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="sub_category" type="Panel" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="VBoxContainer/sub_category"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 4.0
offset_right = 4.0
offset_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "Test Suite Template
"

[node name="EdiorLayout" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="Editor" type="CodeEdit" parent="VBoxContainer/EdiorLayout"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="MarginContainer" type="MarginContainer" parent="VBoxContainer/EdiorLayout/Editor"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -31.0
grow_horizontal = 2
grow_vertical = 0
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer/EdiorLayout/Editor/MarginContainer"]
layout_mode = 2
size_flags_vertical = 8
alignment = 2

[node name="Tags" type="Button" parent="VBoxContainer/EdiorLayout/Editor/MarginContainer/HBoxContainer"]
layout_mode = 2
tooltip_text = "Shows supported tags."
text = "Supported Tags"

[node name="SelectType" type="OptionButton" parent="VBoxContainer/EdiorLayout/Editor/MarginContainer/HBoxContainer"]
layout_mode = 2
tooltip_text = "Select the script type specific template."
item_count = 2
selected = 0
popup/item_0/text = "GD - GDScript"
popup/item_0/id = 1000
popup/item_1/text = "C# - CSharpScript"
popup/item_1/id = 2000

[node name="Panel" type="MarginContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer/Panel"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
alignment = 2

[node name="Restore" type="Button" parent="VBoxContainer/Panel/HBoxContainer"]
layout_mode = 2
text = "Restore"

[node name="Save" type="Button" parent="VBoxContainer/Panel/HBoxContainer"]
layout_mode = 2
disabled = true
text = "Save"

[node name="Tags" type="PopupPanel" parent="."]
size = Vector2i(300, 100)
unresizable = false
content_scale_aspect = 4

[node name="MarginContainer" type="MarginContainer" parent="Tags"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 4.0
offset_top = 4.0
offset_right = -856.0
offset_bottom = -552.0
grow_horizontal = 2
grow_vertical = 2

[node name="TextEdit" type="CodeEdit" parent="Tags/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
editable = false
context_menu_enabled = false
shortcut_keys_enabled = false
virtual_keyboard_enabled = false

[connection signal="text_changed" from="VBoxContainer/EdiorLayout/Editor" to="." method="_on_Editor_text_changed"]
[connection signal="pressed" from="VBoxContainer/EdiorLayout/Editor/MarginContainer/HBoxContainer/Tags" to="." method="_on_Tags_pressed"]
[connection signal="item_selected" from="VBoxContainer/EdiorLayout/Editor/MarginContainer/HBoxContainer/SelectType" to="." method="_on_SelectType_item_selected"]
[connection signal="pressed" from="VBoxContainer/Panel/HBoxContainer/Restore" to="." method="_on_Restore_pressed"]
[connection signal="pressed" from="VBoxContainer/Panel/HBoxContainer/Save" to="." method="_on_Save_pressed"]
