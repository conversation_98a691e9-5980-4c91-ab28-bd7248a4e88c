using Godot;

public partial class Card : Control
{
	[Export] public string Title { get; set; } = "";
	[Export] public int Attack { get; set; } = 0;
	[Export] public int Health { get; set; } = 0;
	[Export] public int Speed { get; set; } = 0;
	[Export] public int DeploymentTime { get; set; } = 0;
	[Export] public Texture2D CardImage { get; set; }

	private Label titleLabel;
	private Label attackLabel;
	private Label healthLabel;
	private Label speedLabel;
	private Label deploymentLabel;
	private TextureRect imageRect;
	private bool isDragging = false;
	private Vector2 dragOffset;
	private CardHolder originalHolder;

	public override void _Ready()
	{
		titleLabel = GetNode<Label>("VBoxContainer/TitleLabel");
		imageRect = GetNode<TextureRect>("VBoxContainer/ImageRect");
		attackLabel = GetNode<Label>("VBoxContainer/StatsContainer/AttackLabel");
		healthLabel = GetNode<Label>("VBoxContainer/StatsContainer/HealthLabel");
		speedLabel = GetNode<Label>("VBoxContainer/StatsContainer/SpeedLabel");
		deploymentLabel = GetNode<Label>("VBoxContainer/DeploymentLabel");

		UpdateCardDisplay();
	}

	public void UpdateCardDisplay()
	{
		if (titleLabel != null) titleLabel.Text = Title;
		if (attackLabel != null) attackLabel.Text = $"ATK: {Attack}";
		if (healthLabel != null) healthLabel.Text = $"HP: {Health}";
		if (speedLabel != null) speedLabel.Text = $"SPD: {Speed}";
		if (deploymentLabel != null) deploymentLabel.Text = $"Deploy: {DeploymentTime}";
		if (imageRect != null && CardImage != null) imageRect.Texture = CardImage;
	}

	public override void _GuiInput(InputEvent @event)
	{
		if (@event is InputEventMouseButton mouseButton)
		{
			if (mouseButton.ButtonIndex == MouseButton.Left)
			{
				if (mouseButton.Pressed)
				{
					StartDrag(mouseButton.Position);
				}
				else if (isDragging)
				{
					EndDrag();
				}
			}
		}
		else if (@event is InputEventMouseMotion mouseMotion && isDragging)
		{
			GlobalPosition = mouseMotion.GlobalPosition - dragOffset;
		}
	}
	
	private void StartDrag(Vector2 mousePosition)
	{
		isDragging = true;
		dragOffset = mousePosition;
		ZIndex = 100; // Bring to front
		originalHolder = GetParent() as CardHolder;
		
		// Add minimal visual feedback for drag start with reduced vertical movement
		var tween = CreateTween();
		tween.SetParallel(true);
		tween.TweenProperty(this, "scale", Vector2.One * 1.01f, 0.1f)
			 .SetEase(Tween.EaseType.Out)
			 .SetTrans(Tween.TransitionType.Quart);
		tween.TweenProperty(this, "rotation", Mathf.DegToRad(0.5f), 0.1f)
			 .SetEase(Tween.EaseType.Out)
			 .SetTrans(Tween.TransitionType.Quart);
	}
	
	private void EndDrag()
	{
		isDragging = false;
		ZIndex = 0;
		
		// Reset visual feedback smoothly
		var tween = CreateTween();
		tween.SetParallel(true);
		tween.TweenProperty(this, "scale", Vector2.One, 0.15f)
			 .SetEase(Tween.EaseType.Out)
			 .SetTrans(Tween.TransitionType.Quart);
		
		HandleDrop();
	}

	private void HandleDrop()
	{
		// First check for battlefield slots
		var battlefieldSlots = GetTree().GetNodesInGroup("battlefield_slots");
		BattlefieldSlot targetSlot = null;
		
		foreach (BattlefieldSlot slot in battlefieldSlots)
		{
			var rect = new Rect2(slot.GlobalPosition, slot.Size);
			if (rect.HasPoint(GlobalPosition + Size / 2))
			{
				targetSlot = slot;
				break;
			}
		}

		if (targetSlot != null)
		{
			// Successfully dropped on battlefield slot
			if (originalHolder != null)
				originalHolder.RemoveCard(this);
			targetSlot.SummonCard(this);
			return;
		}

		// Then check for card holders
		var cardHolders = GetTree().GetNodesInGroup("card_holders");
		CardHolder targetHolder = null;
		
		foreach (CardHolder holder in cardHolders)
		{
			var rect = new Rect2(holder.GlobalPosition, holder.Size);
			if (rect.HasPoint(GlobalPosition + Size / 2))
			{
				targetHolder = holder;
				break;
			}
		}

		if (targetHolder != null && targetHolder != originalHolder)
		{
			// Successfully dropped on new holder
			if (originalHolder != null)
				originalHolder.RemoveCard(this);
			targetHolder.AddCard(this);
		}
		else if (originalHolder != null)
		{
			// Return to original holder with smooth animation
			originalHolder.ArrangeCards();
		}
		else
		{
			// Fallback: animate back to a safe position
			var tween = CreateTween();
			tween.TweenProperty(this, "position", Vector2.Zero, 0.2f)
				 .SetEase(Tween.EaseType.Out)
				 .SetTrans(Tween.TransitionType.Quart);
		}
	}

	public void SetCardHolder(CardHolder holder)
	{
		originalHolder = holder;
	}
}
