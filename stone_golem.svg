<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="280" viewBox="0 0 200 280" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="200" height="280" fill="#2c2c2c" rx="15"/>
  
  <!-- Stone texture background -->
  <rect x="10" y="10" width="180" height="260" fill="#5a5a5a" rx="10"/>
  
  <!-- <PERSON><PERSON> body (main torso) -->
  <rect x="60" y="120" width="80" height="100" fill="#8b7355" rx="8"/>
  
  <!-- Go<PERSON> head -->
  <rect x="70" y="80" width="60" height="50" fill="#8b7355" rx="6"/>
  
  <!-- Eyes (glowing red) -->
  <circle cx="85" cy="100" r="4" fill="#ff4444"/>
  <circle cx="115" cy="100" r="4" fill="#ff4444"/>
  
  <!-- Arms -->
  <rect x="40" y="130" width="20" height="60" fill="#8b7355" rx="4"/>
  <rect x="140" y="130" width="20" height="60" fill="#8b7355" rx="4"/>
  
  <!-- Legs -->
  <rect x="70" y="220" width="25" height="40" fill="#8b7355" rx="4"/>
  <rect x="105" y="220" width="25" height="40" fill="#8b7355" rx="4"/>
  
  <!-- Stone cracks and details -->
  <path d="M75 85 L85 95 M120 90 L125 100" stroke="#654321" stroke-width="2" fill="none"/>
  <path d="M65 140 L75 150 M125 145 L135 155" stroke="#654321" stroke-width="2" fill="none"/>
  <path d="M80 180 L90 190 M110 185 L120 195" stroke="#654321" stroke-width="2" fill="none"/>
  
  <!-- Chest gem/core -->
  <circle cx="100" cy="160" r="8" fill="#4a90e2" stroke="#2c5aa0" stroke-width="2"/>
  
  <!-- Stone texture dots -->
  <circle cx="90" cy="110" r="1" fill="#654321"/>
  <circle cx="110" cy="115" r="1" fill="#654321"/>
  <circle cx="85" cy="170" r="1" fill="#654321"/>
  <circle cx="115" cy="175" r="1" fill="#654321"/>
  <circle cx="75" cy="200" r="1" fill="#654321"/>
  <circle cx="125" cy="205" r="1" fill="#654321"/>
  
  <!-- Ground/base -->
  <ellipse cx="100" cy="265" rx="40" ry="8" fill="#3a3a3a"/>
  
  <!-- Title area -->
  <rect x="15" y="20" width="170" height="30" fill="#1a1a1a" rx="5"/>
  <text x="100" y="40" text-anchor="middle" fill="#cccccc" font-family="Arial, sans-serif" font-size="16" font-weight="bold">STONE GOLEM</text>
</svg>