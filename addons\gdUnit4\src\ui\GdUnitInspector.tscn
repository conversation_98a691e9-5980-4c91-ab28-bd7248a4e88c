[gd_scene load_steps=8 format=3 uid="uid://mpo5o6d4uybu"]

[ext_resource type="PackedScene" path="res://addons/gdUnit4/src/ui/parts/InspectorToolBar.tscn" id="1"]
[ext_resource type="PackedScene" path="res://addons/gdUnit4/src/ui/parts/InspectorProgressBar.tscn" id="2"]
[ext_resource type="PackedScene" path="res://addons/gdUnit4/src/ui/parts/InspectorStatusBar.tscn" id="3"]
[ext_resource type="PackedScene" path="res://addons/gdUnit4/src/ui/parts/InspectorMonitor.tscn" id="4"]
[ext_resource type="Script" path="res://addons/gdUnit4/src/ui/GdUnitInspector.gd" id="5"]
[ext_resource type="PackedScene" path="res://addons/gdUnit4/src/ui/parts/InspectorTreePanel.tscn" id="7"]
[ext_resource type="PackedScene" path="res://addons/gdUnit4/src/network/GdUnitServer.tscn" id="7_721no"]

[node name="GdUnit" type="Panel"]
use_parent_material = true
clip_contents = true
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
size_flags_horizontal = 11
size_flags_vertical = 3
focus_mode = 2
script = ExtResource("5")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
use_parent_material = true
clip_contents = true
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
size_flags_vertical = 11

[node name="Header" type="VBoxContainer" parent="VBoxContainer"]
use_parent_material = true
clip_contents = true
layout_mode = 2
size_flags_horizontal = 9

[node name="ToolBar" parent="VBoxContainer/Header" instance=ExtResource("1")]
layout_mode = 2
size_flags_vertical = 1

[node name="ProgressBar" parent="VBoxContainer/Header" instance=ExtResource("2")]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 5
max_value = 0.0

[node name="StatusBar" parent="VBoxContainer/Header" instance=ExtResource("3")]
layout_mode = 2
size_flags_horizontal = 11

[node name="MainPanel" parent="VBoxContainer" instance=ExtResource("7")]
unique_name_in_owner = true
layout_mode = 2

[node name="Monitor" parent="VBoxContainer" instance=ExtResource("4")]
layout_mode = 2

[node name="event_server" parent="." instance=ExtResource("7_721no")]

[connection signal="request_discover_tests" from="VBoxContainer/Header/StatusBar" to="." method="_on_status_bar_request_discover_tests"]
[connection signal="select_error_next" from="VBoxContainer/Header/StatusBar" to="VBoxContainer/MainPanel" method="_on_select_next_item_by_state" binds= [6]]
[connection signal="select_error_prevous" from="VBoxContainer/Header/StatusBar" to="VBoxContainer/MainPanel" method="_on_select_previous_item_by_state" binds= [6]]
[connection signal="select_failure_next" from="VBoxContainer/Header/StatusBar" to="VBoxContainer/MainPanel" method="_on_select_next_item_by_state" binds= [5]]
[connection signal="select_failure_prevous" from="VBoxContainer/Header/StatusBar" to="VBoxContainer/MainPanel" method="_on_select_previous_item_by_state" binds= [5]]
[connection signal="select_flaky_next" from="VBoxContainer/Header/StatusBar" to="VBoxContainer/MainPanel" method="_on_select_next_item_by_state" binds= [4]]
[connection signal="select_flaky_prevous" from="VBoxContainer/Header/StatusBar" to="VBoxContainer/MainPanel" method="_on_select_previous_item_by_state" binds= [4]]
[connection signal="tree_view_mode_changed" from="VBoxContainer/Header/StatusBar" to="VBoxContainer/MainPanel" method="_on_status_bar_tree_view_mode_changed"]
[connection signal="jump_to_orphan_nodes" from="VBoxContainer/Monitor" to="VBoxContainer/MainPanel" method="select_first_orphan"]
