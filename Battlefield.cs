using Godot;
using System.Collections.Generic;

public partial class Battlefield : Control
{
	[Export] public int SlotCount { get; set; } = 5;
	[Export] public float SlotSpacing { get; set; } = 20.0f;
	[Export] public Vector2 SlotSize { get; set; } = new Vector2(120, 180);
	
	private List<BattlefieldSlot> slots = new List<BattlefieldSlot>();
	private HBoxContainer slotsContainer;

	public override void _Ready()
	{
		CreateSlotsContainer();
		CreateSlots();
	}

	private void CreateSlotsContainer()
	{
		slotsContainer = new HBoxContainer();
		slotsContainer.Name = "SlotsContainer";
		slotsContainer.AddThemeConstantOverride("separation", (int)SlotSpacing);
		
		// Center the container
		slotsContainer.AnchorLeft = 0.5f;
		slotsContainer.AnchorRight = 0.5f;
		slotsContainer.AnchorTop = 0.5f;
		slotsContainer.AnchorBottom = 0.5f;
		slotsContainer.GrowHorizontal = Control.GrowDirection.Both;
		slotsContainer.GrowVertical = Control.GrowDirection.Both;
		
		AddChild(slotsContainer);
	}

	private void CreateSlots()
	{
		for (int i = 0; i < SlotCount; i++)
		{
			var slot = new BattlefieldSlot();
			slot.Name = $"Slot_{i}";
			slot.SlotIndex = i;
			slot.CustomMinimumSize = SlotSize;
			slot.Size = SlotSize;
			
			slots.Add(slot);
			slotsContainer.AddChild(slot);
		}
	}

	public BattlefieldSlot GetSlotAt(Vector2 globalPosition)
	{
		foreach (var slot in slots)
		{
			var rect = new Rect2(slot.GlobalPosition, slot.Size);
			if (rect.HasPoint(globalPosition))
			{
				return slot;
			}
		}
		return null;
	}

	public List<Card> GetAllSummonedCards()
	{
		var cards = new List<Card>();
		foreach (var slot in slots)
		{
			if (slot.HasCard())
			{
				cards.Add(slot.GetCard());
			}
		}
		return cards;
	}

	public void ClearAllSlots()
	{
		foreach (var slot in slots)
		{
			slot.ClearSlot();
		}
	}
}